#!/bin/bash

# Vidur 模拟运行脚本
# 使用分离的配置文件运行 vidur 模拟

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "vidur/main.py" ]; then
    print_error "vidur/main.py 未找到。请在 vidur 项目根目录运行此脚本。"
    exit 1
fi

# 检查配置文件
SHARED_CONFIG="validation/config_shared.yaml"
VIDUR_CONFIG="validation/config_vidur.yaml"

if [ ! -f "$SHARED_CONFIG" ]; then
    print_error "共享配置文件未找到: $SHARED_CONFIG"
    exit 1
fi

if [ ! -f "$VIDUR_CONFIG" ]; then
    print_error "Vidur 配置文件未找到: $VIDUR_CONFIG"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "未检测到虚拟环境。请确保已激活 Python 环境。"
    print_warning "可以使用以下命令激活:"
    print_warning "  - conda: mamba activate ./env"
    print_warning "  - venv: source .venv/bin/activate"
fi

# 检查数据文件
TRACE_FILE="./data/processed_traces/splitwise_conv.csv"
if [ ! -f "$TRACE_FILE" ]; then
    print_warning "跟踪文件未找到: $TRACE_FILE"
    print_warning "模拟可能会失败。请确保在 data/processed_traces/ 目录中有所需的跟踪数据。"
fi

print_info "开始运行 Vidur LLM 推理模拟..."
print_info "配置文件:"
print_info "  - 共享配置: $SHARED_CONFIG"
print_info "  - Vidur 配置: $VIDUR_CONFIG"

# 使用 Python 脚本运行
python validation/run_comparison.py --mode vidur \
    --shared-config "$SHARED_CONFIG" \
    --vidur-config "$VIDUR_CONFIG"

# 检查执行结果
if [ $? -eq 0 ]; then
    print_info "Vidur 模拟完成成功！"
    print_info "结果应该在 simulator_output/ 目录中可用。"
    
    # 查找最新的输出目录
    LATEST_OUTPUT=$(ls -t simulator_output/ 2>/dev/null | head -n1)
    if [ -n "$LATEST_OUTPUT" ]; then
        print_info "最新输出目录: simulator_output/$LATEST_OUTPUT"
    fi
else
    print_error "Vidur 模拟失败，退出码: $?"
    exit 1
fi
