# Vidur vs vLLM 对比验证

本目录包含用于对比 Vidur 模拟器和 vLLM 服务器性能的配置文件和脚本。

## 文件结构

```
validation/
├── config_shared.yaml      # 共享配置参数
├── config_vidur.yaml       # Vidur 特有配置参数
├── config_vllm.yaml        # vLLM 特有配置参数
├── run_comparison.py       # 主要的对比脚本
├── run_vidur.sh           # Vidur 模拟启动脚本
├── run_vllm.sh            # vLLM 服务器启动脚本
└── README.md              # 本文件
```

## 配置文件说明

### config_shared.yaml
包含 Vidur 和 vLLM 都需要的通用参数：
- 模型名称和配置
- 并行设置
- 设备配置
- KV Cache 设置
- 调度参数
- 测试参数

### config_vidur.yaml
包含 Vidur 模拟器特有的参数：
- 集群和副本配置
- 调度器配置（使用 vLLM 调度器进行对比）
- 请求生成器配置
- 执行时间预测器配置
- 指标收集配置

### config_vllm.yaml
包含 vLLM 服务器特有的参数：
- 本地模型路径
- 服务器配置
- 前端设置
- 加载和缓存配置
- 并行和调度配置

## 使用方法

### 1. 环境准备

确保已激活正确的 Python 环境：
```bash
# 使用 conda/mamba
mamba activate ./env

# 或使用 venv
source .venv/bin/activate
```

### 2. 运行 Vidur 模拟

```bash
# 使用脚本运行
chmod +x validation/run_vidur.sh
./validation/run_vidur.sh

# 或直接使用 Python
python validation/run_comparison.py --mode vidur
```

### 3. 启动 vLLM 服务器

```bash
# 使用脚本启动
chmod +x validation/run_vllm.sh
./validation/run_vllm.sh

# 或直接使用 Python
python validation/run_comparison.py --mode vllm
```

### 4. 同时运行两者

```bash
python validation/run_comparison.py --mode both
```

## 配置参数对应关系

### 共享参数
| 参数名 | Vidur 参数 | vLLM 参数 | 说明 |
|--------|------------|-----------|------|
| model_name | --replica_config_model_name | --model | 模型名称 |
| tensor_parallel_size | --replica_config_tensor_parallel_size | --tensor-parallel-size | 张量并行大小 |
| pipeline_parallel_size | --replica_config_num_pipeline_stages | --pipeline-parallel-size | 流水线并行大小 |
| max_model_len | 通过 max_tokens 设置 | --max-model-len | 最大模型长度 |
| block_size | --vllm_scheduler_config_block_size | --block-size | 块大小 |
| max_num_seqs | --vllm_scheduler_config_batch_size_cap | --max-num-seqs | 最大序列数 |
| max_num_batched_tokens | --vllm_scheduler_config_max_tokens_in_batch | --max-num-batched-tokens | 最大批处理令牌数 |

### Vidur 特有参数
- 使用 vLLM 调度器 (`--replica_scheduler_config_type vllm`)
- 执行时间预测器配置
- 请求生成器配置
- 指标收集配置

### vLLM 特有参数
- 本地模型路径：`/share_data/llm_weights/Meta-Llama-3-8B`
- 服务器配置（host, port 等）
- 前端和 API 配置

## 注意事项

1. **模型路径**：
   - Vidur 使用 HuggingFace 模型名称
   - vLLM 使用本地模型路径

2. **调度器对比**：
   - Vidur 配置为使用 vLLM 调度器以确保公平对比
   - 两者使用相同的调度参数

3. **数据文件**：
   - 确保 `./data/processed_traces/splitwise_conv.csv` 文件存在
   - 这是用于生成测试请求的跟踪文件

4. **资源配置**：
   - 两者使用相同的 GPU 内存利用率 (0.9)
   - 相同的并行配置和批处理设置

## 结果分析

### Vidur 输出
- 结果保存在 `simulator_output/` 目录
- 包含详细的性能指标和分析图表
- 可以查看延迟、吞吐量等关键指标

### vLLM 输出
- 通过 OpenAI 兼容的 API 提供服务
- 可以使用客户端工具测试性能
- 服务器日志包含性能统计信息

## 配置验证

在运行对比测试之前，可以使用测试脚本验证配置：

```bash
python validation/test_config.py
```

该脚本会检查：
- 配置文件语法正确性
- 必需参数完整性
- 模型和数据文件存在性
- 参数一致性

## 实验流程

本项目采用两阶段对比实验：

### 阶段 1: 生成测试数据
1. **生成测试数据集**：
   ```bash
   python validation/generate_vllm_test_data.py --num-requests 100
   ```

2. **运行 vLLM 基准测试**：
   ```bash
   python validation/test_data/run_vllm_benchmark.py
   ```

### 阶段 2: Vidur 模拟对比
3. **更新 Vidur 配置**：
   将生成的 CSV 文件路径更新到 `config_vidur.yaml` 中的 `trace_file` 字段

4. **运行 Vidur 模拟**：
   ```bash
   ./validation/run_vidur.sh
   ```

5. **对比结果**：
   比较 vLLM 实际运行结果和 Vidur 模拟结果

## 快速开始

1. **验证配置**：
   ```bash
   python validation/test_config.py
   ```

2. **生成并运行完整测试**：
   ```bash
   # 生成测试数据
   python validation/generate_vllm_test_data.py

   # 运行 vLLM 测试（按提示执行生成的脚本）

   # 更新配置文件中的 trace_file 路径

   # 运行 Vidur 模拟
   ./validation/run_vidur.sh
   ```

## 故障排除

1. **模型未找到**：确保模型路径正确
2. **配置文件错误**：检查 YAML 语法
3. **环境问题**：确保已安装所有依赖
4. **数据文件缺失**：检查跟踪文件是否存在
5. **权限问题**：确保脚本有执行权限 (`chmod +x validation/*.sh`)

## 自定义配置

可以根据需要修改配置文件中的参数：
- 调整批处理大小和序列长度
- 修改并行配置
- 更改测试参数（QPS、请求数量等）
- 添加或删除特定的配置选项

## 配置文件设计原则

1. **参数分离**：共享参数放在 `config_shared.yaml`，特有参数分别放在各自的配置文件中
2. **一致性保证**：关键对比参数在两个系统中保持一致
3. **公平对比**：Vidur 使用 vLLM 调度器确保调度策略一致
4. **易于维护**：参数修改只需在一个地方进行
