#!/usr/bin/env python3
"""
配置文件测试脚本
验证配置文件是否能正确生成命令行参数
"""

import yaml
import sys
import os

def load_config(config_path):
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def merge_configs(shared_config, specific_config):
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged

def test_vidur_config():
    """测试 Vidur 配置"""
    print("=== 测试 Vidur 配置 ===")
    
    try:
        shared_config = load_config("validation/config_shared.yaml")
        vidur_config = load_config("validation/config_vidur.yaml")
        merged_config = merge_configs(shared_config, vidur_config)
        
        print("✓ 配置文件加载成功")
        print(f"✓ 模型名称: {merged_config['model_name']}")
        print(f"✓ 设备: {merged_config['device']}")
        print(f"✓ 调度器类型: {merged_config['replica_scheduler_config']['type']}")
        print(f"✓ 请求数量: {merged_config['num_requests']}")
        print(f"✓ QPS: {merged_config['qps']}")
        
        # 检查关键参数
        required_params = [
            'model_name', 'device', 'tensor_parallel_size', 'pipeline_parallel_size',
            'max_num_seqs', 'max_num_batched_tokens', 'batch_size_cap', 'block_size',
            'num_requests', 'qps', 'max_tokens'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in merged_config:
                missing_params.append(param)
        
        if missing_params:
            print(f"✗ 缺少必需参数: {missing_params}")
            return False
        else:
            print("✓ 所有必需参数都存在")
            return True
            
    except Exception as e:
        print(f"✗ Vidur 配置测试失败: {e}")
        return False

def test_vllm_config():
    """测试 vLLM 配置"""
    print("\n=== 测试 vLLM 配置 ===")
    
    try:
        shared_config = load_config("validation/config_shared.yaml")
        vllm_config = load_config("validation/config_vllm.yaml")
        merged_config = merge_configs(shared_config, vllm_config)
        
        print("✓ 配置文件加载成功")
        print(f"✓ 模型路径: {merged_config['model']}")
        print(f"✓ 主机: {merged_config['host']}")
        print(f"✓ 端口: {merged_config['port']}")
        print(f"✓ 张量并行大小: {merged_config['tensor_parallel_size']}")
        print(f"✓ 最大序列数: {merged_config['max_num_seqs']}")
        
        # 检查关键参数
        required_params = [
            'model', 'host', 'port', 'tensor_parallel_size', 'pipeline_parallel_size',
            'max_model_len', 'dtype', 'max_num_seqs', 'max_num_batched_tokens',
            'block_size', 'gpu_memory_utilization'
        ]
        
        missing_params = []
        for param in required_params:
            if param not in merged_config:
                missing_params.append(param)
        
        if missing_params:
            print(f"✗ 缺少必需参数: {missing_params}")
            return False
        else:
            print("✓ 所有必需参数都存在")
            
        # 检查模型路径是否存在
        model_path = merged_config['model']
        if os.path.exists(model_path):
            print(f"✓ 模型路径存在: {model_path}")
        else:
            print(f"⚠ 模型路径不存在: {model_path}")
            print("  请确保模型已下载到指定路径")
            
        return True
            
    except Exception as e:
        print(f"✗ vLLM 配置测试失败: {e}")
        return False

def test_trace_file():
    """测试跟踪文件配置"""
    print("\n=== 测试跟踪文件配置 ===")

    try:
        vidur_config = load_config("validation/config_vidur.yaml")
        vllm_config = load_config("validation/config_vllm.yaml")

        # 检查 Vidur 配置中的 trace_file 占位符
        vidur_trace = vidur_config.get('length_generator_config', {}).get('trace_file')
        if vidur_trace == "PLACEHOLDER_VIDUR_TRACE_FILE":
            print("✓ Vidur 跟踪文件占位符配置正确")
        else:
            print(f"⚠ Vidur 跟踪文件配置异常: {vidur_trace}")

        # 检查 vLLM 配置中的 test_dataset 占位符
        vllm_dataset = vllm_config.get('test_dataset')
        if vllm_dataset == "PLACEHOLDER_VLLM_DATASET":
            print("✓ vLLM 测试数据集占位符配置正确")
        else:
            print(f"⚠ vLLM 测试数据集配置异常: {vllm_dataset}")

        print("✓ 跟踪文件配置检查完成")
        print("  注意：需要根据实际测试数据替换占位符")
        return True

    except Exception as e:
        print(f"✗ 跟踪文件配置测试失败: {e}")
        return False

def test_parameter_consistency():
    """测试参数一致性"""
    print("\n=== 测试参数一致性 ===")
    
    try:
        shared_config = load_config("validation/config_shared.yaml")
        vidur_config = load_config("validation/config_vidur.yaml")
        vllm_config = load_config("validation/config_vllm.yaml")
        
        vidur_merged = merge_configs(shared_config, vidur_config)
        vllm_merged = merge_configs(shared_config, vllm_config)
        
        # 检查关键参数是否一致
        consistency_params = [
            'tensor_parallel_size', 'pipeline_parallel_size', 'max_num_seqs',
            'max_num_batched_tokens', 'block_size', 'seed'
        ]
        
        inconsistent_params = []
        for param in consistency_params:
            if vidur_merged.get(param) != vllm_merged.get(param):
                inconsistent_params.append(param)
                print(f"⚠ 参数不一致 {param}: Vidur={vidur_merged.get(param)}, vLLM={vllm_merged.get(param)}")
        
        if inconsistent_params:
            print(f"⚠ 发现 {len(inconsistent_params)} 个不一致的参数")
            print("  这可能影响对比结果的公平性")
            return False
        else:
            print("✓ 所有关键参数在两个配置中都一致")
            return True
            
    except Exception as e:
        print(f"✗ 参数一致性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始配置文件测试...\n")
    
    # 检查配置文件是否存在
    config_files = [
        "validation/config_shared.yaml",
        "validation/config_vidur.yaml", 
        "validation/config_vllm.yaml"
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            print(f"✗ 配置文件不存在: {config_file}")
            sys.exit(1)
    
    # 运行测试
    tests = [
        test_vidur_config,
        test_vllm_config,
        test_trace_file,
        test_parameter_consistency
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！配置文件准备就绪。")
        sys.exit(0)
    else:
        print("⚠ 部分测试未通过，请检查配置。")
        sys.exit(1)

if __name__ == "__main__":
    main()
