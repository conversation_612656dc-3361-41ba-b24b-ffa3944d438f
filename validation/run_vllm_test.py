#!/usr/bin/env python3
"""
简单的 vLLM 测试脚本
加载 prompts 并运行推理，输出结果供 Vidur 对比使用
"""

import json
import time
import csv
import logging
import sys
from datetime import datetime
from pathlib import Path
from vllm import LLM, SamplingParams


def setup_logging(log_dir: str = "validation/logs/client") -> logging.Logger:
    """设置日志系统"""
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/vllm_test_{timestamp}.log"
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建 logger
    logger = logging.getLogger('vllm_test')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', date_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger

def main():
    # 设置日志系统
    logger = setup_logging()
    
    json_path = "validation/test_data/prompts_20250806_103014.json"
    logger.info(f"加载 prompts 文件: {json_path}")
    
    # 加载 prompts
    with open(json_path, "r", encoding="utf-8") as f:
        prompts = json.load(f)

    logger.info(f"加载了 {len(prompts)} 个 prompts")

    # 初始化 vLLM（使用配置文件中的参数）
    logger.info("初始化 vLLM 模型...")
    llm = LLM(
        model="/share_data/llm_weights/Meta-Llama-3-8B",
        tensor_parallel_size=1,
        max_model_len=4096,
        gpu_memory_utilization=0.9,
        block_size=16,
        max_num_seqs=128,
        max_num_batched_tokens=4096,
        seed=42,
        trust_remote_code=False,
    )
    logger.info("vLLM 模型初始化完成")

    # 设置采样参数
    sampling_params = SamplingParams(
        max_tokens=128,
        temperature=0.0,  # 确定性输出
        top_p=1.0,
        ignore_eos=True,
    )
    logger.info(f"采样参数: max_tokens=128, temperature=0.0, top_p=1.0")

    logger.info("开始推理...")

    # 记录开始时间
    start_time = time.time()

    # 执行推理
    outputs = llm.generate(prompts, sampling_params, use_tqdm=True)

    # 记录结束时间
    end_time = time.time()

    # 收集结果并保存为 Vidur 可用的格式
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = f"validation/test_data/vllm_results_{timestamp}.csv"

    logger.info(f"保存结果到: {csv_path}")
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for i, (prompt, output) in enumerate(zip(prompts, outputs)):
            # 计算实际的 token 数量
            prompt_tokens = len(llm.get_tokenizer().encode(prompt))
            decode_tokens = len(output.outputs[0].token_ids)

            writer.writerow({
                'request_id': i,
                'arrived_at': start_time + i * 0.01,  # 假设请求间隔 0.01 秒
                'num_prefill_tokens': prompt_tokens,
                'num_decode_tokens': decode_tokens,
            })

    # 打印统计信息
    total_time = end_time - start_time
    total_prompts = len(prompts)
    total_tokens = sum(len(output.outputs[0].token_ids) for output in outputs)
    throughput = total_tokens / total_time

    logger.info(f"=== vLLM 测试完成 ===")
    logger.info(f"总请求数: {total_prompts}")
    logger.info(f"总时间: {total_time:.2f} 秒")
    logger.info(f"总输出令牌数: {total_tokens}")
    logger.info(f"吞吐量: {throughput:.2f} tokens/sec")
    logger.info(f"结果已保存到: {csv_path}")
    logger.info(f"请将此 CSV 文件路径更新到 config_vidur.yaml 的 trace_file 字段中")

if __name__ == "__main__":
    main()
