#!/usr/bin/env python3

import json

def extract_context_values(input_file, output_file, repeat_num=None):
    """
    Extract 'Context' values from combined_dataset.jsonl and save to JSON format.
    
    Args:
        input_file: Path to the input JSONL file
        output_file: Path to the output JSON file
        repeat_num: Maximum number of times each context can appear. 
                   If None, no deduplication is performed.
    """
    context_values = []
    context_count = {}  # Track count of each context
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                context = data.get('Context', '')
                if context:  # Only add non-empty contexts
                    if repeat_num is None:
                        # No deduplication, add all contexts
                        context_values.append(context)
                    else:
                        # Check if this context has appeared less than repeat_num times
                        current_count = context_count.get(context, 0)
                        if current_count < repeat_num:
                            context_values.append(context)
                            context_count[context] = current_count + 1
                        
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
    
    # Save to JSON file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(context_values, f, ensure_ascii=False, indent=2)
    
    if repeat_num is not None:
        print(f"Extracted {len(context_values)} context values (max {repeat_num} repeats per context) to {output_file}")
        print(f"Unique contexts: {len(context_count)}")
    else:
        print(f"Extracted {len(context_values)} context values to {output_file}")


def add_suffix(filename, repeat_num):
    """
    Add suffix to the filename based on repeat_num.
    
    Args:
        filename: Original filename
        repeat_num: Maximum number of times each context can appear
    Returns:
        New filename with suffix
    """
    if repeat_num is None:
        return filename
    elif repeat_num == 1:
        return f"{filename.split('.')[0]}_unique.json"
    else:
        return f"{filename.split('.')[0]}_repeat_{repeat_num}.json"


if __name__ == "__main__":
    input_file = "validation/datasets/combined_dataset.jsonl"
    output_file = "validation/datasets/context_values.json"
    
    repeat_num = 1  # Set to None for no deduplication, or an integer for max repeats
    output_file = add_suffix(output_file, repeat_num)

    extract_context_values(input_file, output_file, repeat_num=repeat_num)