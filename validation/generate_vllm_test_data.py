#!/usr/bin/env python3
"""
vLLM 测试数据生成脚本
使用 vLLM 内置的 benchmark 工具生成测试数据，用于后续与 Vidur 对比
"""

import argparse
import asyncio
import csv
import json
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

import yaml
from transformers import AutoTokenizer

# 导入 vLLM benchmark 相关模块
from vllm.benchmarks.datasets import RandomDataset, get_samples
from vllm.benchmarks.serve import BenchmarkMetrics
from vllm.benchmarks.endpoint_request_func import ASYNC_REQUEST_FUNCS
from vllm.transformers_utils.tokenizer import get_tokenizer


def load_config(config_path: str) -> Dict[str, Any]:
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def merge_configs(shared_config: Dict[str, Any], specific_config: Dict[str, Any]) -> Dict[str, Any]:
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged


def generate_random_dataset(tokenizer, num_requests: int = 100, input_len: int = 512, output_len: int = 128) -> List[Dict[str, Any]]:
    """生成随机测试数据集"""
    print(f"生成随机数据集: {num_requests} 个请求, 输入长度={input_len}, 输出长度={output_len}")
    
    dataset = RandomDataset(random_seed=42)
    requests = dataset.sample(
        tokenizer=tokenizer,
        num_requests=num_requests,
        input_len=input_len,
        output_len=output_len,
        range_ratio=0.1  # 10% 的长度变化范围
    )
    
    # 转换为适合保存的格式
    test_data = []
    for i, request in enumerate(requests):
        test_data.append({
            'request_id': i,
            'prompt': request.prompt,
            'prompt_len': request.prompt_len,
            'expected_output_len': request.expected_output_len,
            'arrived_at': i * 0.1,  # 简单的到达时间模拟，每0.1秒一个请求
        })
    
    return test_data


def save_test_data_csv(test_data: List[Dict[str, Any]], output_path: str):
    """保存测试数据为 CSV 格式（适合 Vidur 使用）"""
    print(f"保存测试数据到: {output_path}")
    
    with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens', 'prompt']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for data in test_data:
            writer.writerow({
                'request_id': data['request_id'],
                'arrived_at': data['arrived_at'],
                'num_prefill_tokens': data['prompt_len'],
                'num_decode_tokens': data['expected_output_len'],
                'prompt': data['prompt']
            })


def save_test_data_json(test_data: List[Dict[str, Any]], output_path: str):
    """保存测试数据为 JSON 格式（适合 vLLM 使用）"""
    print(f"保存测试数据到: {output_path}")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)


def create_vllm_benchmark_script(config: Dict[str, Any], test_data_path: str, output_dir: str) -> str:
    """创建 vLLM benchmark 脚本"""
    script_content = f'''#!/usr/bin/env python3
"""
vLLM Benchmark 脚本
自动生成，用于测试 vLLM 性能并收集指标
"""

import json
import time
import csv
from datetime import datetime
from vllm import LLM, SamplingParams

def main():
    # 加载测试数据
    with open("{test_data_path}", "r", encoding="utf-8") as f:
        test_data = json.load(f)
    
    # 初始化 vLLM
    llm = LLM(
        model="{config['model']}",
        tensor_parallel_size={config['tensor_parallel_size']},
        max_model_len={config['max_model_len']},
        gpu_memory_utilization={config['gpu_memory_utilization']},
        block_size={config['block_size']},
        max_num_seqs={config['max_num_seqs']},
        max_num_batched_tokens={config['max_num_batched_tokens']},
        seed={config['seed']},
        trust_remote_code={str(config['trust_remote_code']).lower()},
    )
    
    # 准备请求
    prompts = [data['prompt'] for data in test_data]
    sampling_params = [
        SamplingParams(
            max_tokens=data['expected_output_len'],
            temperature=0.0,  # 确定性输出
            top_p=1.0,
            ignore_eos=True,
        ) for data in test_data
    ]
    
    print(f"开始测试 {{len(prompts)}} 个请求...")
    
    # 执行推理并记录时间
    start_time = time.time()
    outputs = llm.generate(prompts, sampling_params, use_tqdm=True)
    end_time = time.time()
    
    # 收集结果
    results = []
    for i, (data, output) in enumerate(zip(test_data, outputs)):
        result = {{
            'request_id': data['request_id'],
            'arrived_at': data['arrived_at'],
            'num_prefill_tokens': data['prompt_len'],
            'num_decode_tokens': len(output.outputs[0].token_ids),
            'prompt': data['prompt'],
            'response': output.outputs[0].text,
            'finish_time': start_time + (i + 1) * (end_time - start_time) / len(prompts),  # 估算
        }}
        results.append(result)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存为 CSV（用于 Vidur 对比）
    csv_path = f"{output_dir}/vllm_results_{{timestamp}}.csv"
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens', 'finish_time']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow({{
                'request_id': result['request_id'],
                'arrived_at': result['arrived_at'],
                'num_prefill_tokens': result['num_prefill_tokens'],
                'num_decode_tokens': result['num_decode_tokens'],
                'finish_time': result['finish_time'],
            }})
    
    # 保存详细结果为 JSON
    json_path = f"{output_dir}/vllm_detailed_results_{{timestamp}}.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 计算基本统计信息
    total_time = end_time - start_time
    total_tokens = sum(result['num_prefill_tokens'] + result['num_decode_tokens'] for result in results)
    throughput = total_tokens / total_time
    
    print(f"\\n=== vLLM 测试结果 ===")
    print(f"总请求数: {{len(results)}}")
    print(f"总时间: {{total_time:.2f}} 秒")
    print(f"总令牌数: {{total_tokens}}")
    print(f"吞吐量: {{throughput:.2f}} tokens/sec")
    print(f"结果已保存到:")
    print(f"  CSV: {{csv_path}}")
    print(f"  JSON: {{json_path}}")

if __name__ == "__main__":
    main()
'''
    
    script_path = f"{output_dir}/run_vllm_benchmark.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 添加执行权限
    import os
    os.chmod(script_path, 0o755)
    
    return script_path


def main():
    parser = argparse.ArgumentParser(description="生成 vLLM 测试数据")
    parser.add_argument("--shared-config", default="validation/config_shared.yaml",
                       help="共享配置文件路径")
    parser.add_argument("--vllm-config", default="validation/config_vllm.yaml",
                       help="vLLM 配置文件路径")
    parser.add_argument("--num-requests", type=int, default=100,
                       help="生成的请求数量")
    parser.add_argument("--input-len", type=int, default=512,
                       help="输入长度")
    parser.add_argument("--output-len", type=int, default=128,
                       help="输出长度")
    parser.add_argument("--output-dir", default="validation/test_data",
                       help="输出目录")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载配置
    shared_config = load_config(args.shared_config)
    vllm_config = load_config(args.vllm_config)
    merged_config = merge_configs(shared_config, vllm_config)
    
    print("=== 生成 vLLM 测试数据 ===")
    print(f"模型: {merged_config['model_name']}")
    print(f"请求数量: {args.num_requests}")
    print(f"输入长度: {args.input_len}")
    print(f"输出长度: {args.output_len}")
    
    # 初始化 tokenizer
    tokenizer = get_tokenizer(merged_config['model_name'])
    
    # 生成测试数据
    test_data = generate_random_dataset(
        tokenizer=tokenizer,
        num_requests=args.num_requests,
        input_len=args.input_len,
        output_len=args.output_len
    )
    
    # 保存数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = output_dir / f"test_data_{timestamp}.csv"
    json_path = output_dir / f"test_data_{timestamp}.json"
    
    save_test_data_csv(test_data, str(csv_path))
    save_test_data_json(test_data, str(json_path))
    
    # 创建 vLLM benchmark 脚本
    script_path = create_vllm_benchmark_script(merged_config, str(json_path), str(output_dir))
    
    print(f"\n=== 生成完成 ===")
    print(f"测试数据 (CSV): {csv_path}")
    print(f"测试数据 (JSON): {json_path}")
    print(f"vLLM 测试脚本: {script_path}")
    print(f"\n下一步:")
    print(f"1. 运行 vLLM 测试: python {script_path}")
    print(f"2. 将生成的 CSV 文件用于 Vidur 测试")
    print(f"3. 对比两者的性能结果")


if __name__ == "__main__":
    main()
