#!/bin/bash

# vLLM 服务器启动脚本
# 使用分离的配置文件启动 vLLM 服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查配置文件
SHARED_CONFIG="validation/config_shared.yaml"
VLLM_CONFIG="validation/config_vllm.yaml"

if [ ! -f "$SHARED_CONFIG" ]; then
    print_error "共享配置文件未找到: $SHARED_CONFIG"
    exit 1
fi

if [ ! -f "$VLLM_CONFIG" ]; then
    print_error "vLLM 配置文件未找到: $VLLM_CONFIG"
    exit 1
fi

# 检查模型路径
MODEL_PATH="/share_data/llm_weights/Meta-Llama-3-8B"
if [ ! -d "$MODEL_PATH" ]; then
    print_error "模型路径未找到: $MODEL_PATH"
    print_error "请确保模型已下载到指定路径。"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "未检测到虚拟环境。请确保已激活 Python 环境。"
    print_warning "可以使用以下命令激活:"
    print_warning "  - conda: mamba activate ./env"
    print_warning "  - venv: source .venv/bin/activate"
fi

print_info "开始启动 vLLM 服务器..."
print_info "配置文件:"
print_info "  - 共享配置: $SHARED_CONFIG"
print_info "  - vLLM 配置: $VLLM_CONFIG"
print_info "  - 模型路径: $MODEL_PATH"

# 使用 Python 脚本启动服务器
python validation/run_comparison.py --mode vllm \
    --shared-config "$SHARED_CONFIG" \
    --vllm-config "$VLLM_CONFIG"
