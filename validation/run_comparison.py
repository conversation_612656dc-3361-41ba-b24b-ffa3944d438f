#!/usr/bin/env python3
"""
Vidur vs vLLM 对比测试脚本
使用分离的配置文件来运行 vidur 模拟和 vLLM 服务
"""

import yaml
import subprocess
import argparse
import os
import sys
from pathlib import Path

def load_config(config_path):
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def merge_configs(shared_config, specific_config):
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged

def build_vidur_command(config):
    """构建 vidur 命令行参数"""
    cmd = ["python", "-m", "vidur.main"]
    
    # 基础配置
    cmd.extend([
        "--seed", str(config["seed"]),
        "--log_level", config["log_level"],
        "--time_limit", str(config.get("time_limit", 0))
    ])
    
    # 集群配置
    cluster_config = config.get("cluster_config", {})
    cmd.extend([
        "--cluster_config_num_replicas", str(cluster_config.get("num_replicas", 1))
    ])
    
    # 副本配置
    replica_config = config.get("replica_config", {})
    cmd.extend([
        "--replica_config_model_name", config["model_name"],
        "--replica_config_device", config["device"],
        "--replica_config_tensor_parallel_size", str(config["tensor_parallel_size"]),
        "--replica_config_num_pipeline_stages", str(config["pipeline_parallel_size"]),
        "--replica_config_memory_margin_fraction", str(replica_config.get("memory_margin_fraction", 0.1)),
        "--replica_config_network_device", replica_config.get("network_device", "a100_pairwise_nvlink")
    ])
    
    # 调度器配置
    scheduler_config = config.get("replica_scheduler_config", {})
    scheduler_type = scheduler_config.get("type", "vllm")
    cmd.extend([
        "--replica_scheduler_config_type", scheduler_type,
        f"--{scheduler_type}_scheduler_config_batch_size_cap", str(config["batch_size_cap"]),
        f"--{scheduler_type}_scheduler_config_block_size", str(config["block_size"]),
        f"--{scheduler_type}_scheduler_config_max_tokens_in_batch", str(config["max_num_batched_tokens"])
    ])
    
    # 请求生成器配置
    request_config = config.get("request_generator_config", {})
    cmd.extend([
        "--request_generator_config_type", request_config.get("type", "synthetic"),
        "--synthetic_request_generator_config_num_requests", str(config["num_requests"])
    ])
    
    # 长度生成器配置
    length_config = config.get("length_generator_config", {})
    cmd.extend([
        "--length_generator_config_type", length_config.get("type", "trace"),
        "--trace_request_length_generator_config_max_tokens", str(config["max_tokens"]),
        "--trace_request_length_generator_config_trace_file", config["trace_file"]
    ])
    
    # 间隔生成器配置
    interval_config = config.get("interval_generator_config", {})
    cmd.extend([
        "--interval_generator_config_type", interval_config.get("type", "poisson"),
        "--poisson_request_interval_generator_config_qps", str(config["qps"])
    ])
    
    # 执行时间预测器配置
    predictor_config = config.get("execution_time_predictor_config", {})
    predictor_type = predictor_config.get("type", "random_forrest")
    cmd.extend([
        "--execution_time_predictor_config_type", predictor_type,
        f"--{predictor_type}_execution_time_predictor_config_prediction_max_prefill_chunk_size", str(config["max_tokens"]),
        f"--{predictor_type}_execution_time_predictor_config_prediction_max_batch_size", str(config["max_num_seqs"]),
        f"--{predictor_type}_execution_time_predictor_config_prediction_max_tokens_per_request", str(config["max_tokens"])
    ])
    
    return cmd

def build_vllm_command(config):
    """构建 vLLM 服务器命令行参数"""
    cmd = ["python", "-m", "vllm.entrypoints.openai.api_server"]
    
    # 基础配置
    cmd.extend([
        "--model", config["model"],
        "--host", config.get("host", "0.0.0.0"),
        "--port", str(config.get("port", 8000)),
        "--tensor-parallel-size", str(config["tensor_parallel_size"]),
        "--pipeline-parallel-size", str(config["pipeline_parallel_size"]),
        "--max-model-len", str(config["max_model_len"]),
        "--dtype", config["dtype"],
        "--seed", str(config["seed"]),
        "--block-size", str(config["block_size"]),
        "--gpu-memory-utilization", str(config["gpu_memory_utilization"]),
        "--max-num-seqs", str(config["max_num_seqs"]),
        "--max-num-batched-tokens", str(config["max_num_batched_tokens"])
    ])
    
    # 可选配置
    if not config.get("trust_remote_code", False):
        cmd.append("--no-trust-remote-code")
    
    if config.get("disable_log_requests", False):
        cmd.append("--disable-log-requests")
        
    if config.get("disable_log_stats", False):
        cmd.append("--disable-log-stats")
    
    return cmd

def run_vidur_simulation(shared_config_path, vidur_config_path):
    """运行 vidur 模拟"""
    print("=== 运行 Vidur 模拟 ===")
    
    # 加载配置
    shared_config = load_config(shared_config_path)
    vidur_config = load_config(vidur_config_path)
    merged_config = merge_configs(shared_config, vidur_config)
    
    # 构建命令
    cmd = build_vidur_command(merged_config)
    
    print(f"执行命令: {' '.join(cmd)}")
    
    # 运行命令
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Vidur 模拟完成")
        print("输出:", result.stdout[-500:])  # 显示最后500个字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"Vidur 模拟失败: {e}")
        print("错误输出:", e.stderr)
        return False

def start_vllm_server(shared_config_path, vllm_config_path):
    """启动 vLLM 服务器"""
    print("=== 启动 vLLM 服务器 ===")
    
    # 加载配置
    shared_config = load_config(shared_config_path)
    vllm_config = load_config(vllm_config_path)
    merged_config = merge_configs(shared_config, vllm_config)
    
    # 构建命令
    cmd = build_vllm_command(merged_config)
    
    print(f"执行命令: {' '.join(cmd)}")
    print("注意: vLLM 服务器将在后台运行，请手动停止")
    
    # 启动服务器（非阻塞）
    try:
        process = subprocess.Popen(cmd)
        print(f"vLLM 服务器已启动，PID: {process.pid}")
        print(f"服务器地址: http://{merged_config.get('host', '0.0.0.0')}:{merged_config.get('port', 8000)}")
        return process
    except Exception as e:
        print(f"启动 vLLM 服务器失败: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Vidur vs vLLM 对比测试")
    parser.add_argument("--mode", choices=["vidur", "vllm", "both"], default="both",
                       help="运行模式: vidur, vllm, 或 both")
    parser.add_argument("--shared-config", default="validation/config_shared.yaml",
                       help="共享配置文件路径")
    parser.add_argument("--vidur-config", default="validation/config_vidur.yaml",
                       help="Vidur 配置文件路径")
    parser.add_argument("--vllm-config", default="validation/config_vllm.yaml",
                       help="vLLM 配置文件路径")
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    for config_path in [args.shared_config, args.vidur_config, args.vllm_config]:
        if not os.path.exists(config_path):
            print(f"配置文件不存在: {config_path}")
            sys.exit(1)
    
    if args.mode in ["vidur", "both"]:
        success = run_vidur_simulation(args.shared_config, args.vidur_config)
        if not success:
            print("Vidur 模拟失败")
            sys.exit(1)
    
    if args.mode in ["vllm", "both"]:
        process = start_vllm_server(args.shared_config, args.vllm_config)
        if process is None:
            print("vLLM 服务器启动失败")
            sys.exit(1)
        
        if args.mode == "vllm":
            try:
                process.wait()
            except KeyboardInterrupt:
                print("正在停止 vLLM 服务器...")
                process.terminate()

if __name__ == "__main__":
    main()
