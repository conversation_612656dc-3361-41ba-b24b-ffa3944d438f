#!/usr/bin/env python3
"""
vLLM API 客户端脚本
通过 HTTP API 访问 vLLM 服务器，收集性能数据用于与 Vidur 对比
"""

import json
import time
import csv
import asyncio
import aiohttp
from datetime import datetime
from pathlib import Path
import argparse
import yaml
import logging
import sys
from typing import List, Dict, Any
from tqdm import tqdm


def setup_logging(log_dir: str = "validation/logs/client") -> logging.Logger:
    """设置日志系统"""
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/vllm_client_{timestamp}.log"
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建 logger
    logger = logging.getLogger('vllm_client')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', date_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger


def load_config(config_path: str) -> Dict[str, Any]:
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def merge_configs(shared_config: Dict[str, Any], specific_config: Dict[str, Any]) -> Dict[str, Any]:
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged


async def send_request(session: aiohttp.ClientSession, url: str, prompt: str, max_tokens: int, request_id: int, start_time: float, model_name: str, logger: logging.Logger) -> Dict[str, Any]:
    """发送单个请求到 vLLM 服务器"""
    payload = {
        "model": model_name,
        "prompt": prompt,
        # "max_tokens": max_tokens,
        "temperature": 0.0,
        "top_p": 1.0,
        "stream": False
    }
    
    # 记录请求发送时间（相对于开始时间）
    request_start_time = time.time()
    arrived_at = request_start_time - start_time
    
    logger.debug(f"发送请求 {request_id}: prompt长度={len(prompt)}, arrived_at={arrived_at:.3f}")
    
    try:
        async with session.post(url, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                request_end_time = time.time()
                
                # 提取响应信息
                choice = result['choices'][0]
                message = choice['text']
                usage = result['usage']
                latency = request_end_time - request_start_time
                
                logger.info(f"请求 {request_id} 成功: "
                          f"延迟={latency:.3f}s, "
                          f"输入tokens={usage['prompt_tokens']}, "
                          f"输出tokens={usage['completion_tokens']}")
                
                return {
                    'request_id': request_id,
                    'arrived_at': arrived_at,
                    'num_prefill_tokens': usage['prompt_tokens'],
                    'num_decode_tokens': usage['completion_tokens'],
                    'total_tokens': usage['total_tokens'],
                    'response': message,
                    'latency': latency,
                    'success': True
                }
            else:
                error_text = await response.text()
                logger.error(f"请求 {request_id} 失败: HTTP {response.status} - {error_text}")
                return {
                    'request_id': request_id,
                    'arrived_at': arrived_at,
                    'success': False,
                    'error': f"HTTP {response.status}"
                }
    except Exception as e:
        logger.error(f"请求 {request_id} 异常: {e}")
        return {
            'request_id': request_id,
            'arrived_at': arrived_at,
            'success': False,
            'error': str(e)
        }


async def run_benchmark(prompts_file: str, server_url: str, max_tokens: int = 128, qps: float = 1.0, output_dir: str = "validation/test_data", model_name: str = None, logger: logging.Logger = None):
    """运行基准测试"""
    if logger is None:
        logger = logging.getLogger('vllm_client')
    
    # 加载 prompts
    logger.info(f"加载 prompts 文件: {prompts_file}")
    with open(prompts_file, 'r', encoding='utf-8') as f:
        prompts = json.load(f)
    
    # 如果没有指定模型名称，自动获取
    if not model_name:
        logger.info("正在获取可用模型...")
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            models_url = server_url.replace('/v1/completions', '/v1/models')
            try:
                async with session.get(models_url) as response:
                    if response.status == 200:
                        models_data = await response.json()
                        if models_data.get('data'):
                            model_name = models_data['data'][0]['id']
                            logger.info(f"自动选择模型: {model_name}")
                        else:
                            model_name = "/share_data/llm_weights/Meta-Llama-3-8B"  # 默认值
                            logger.warning(f"未找到可用模型，使用默认值: {model_name}")
                    else:
                        model_name = "/share_data/llm_weights/Meta-Llama-3-8B"  # 默认值
                        logger.warning(f"获取模型列表失败 (HTTP {response.status})，使用默认值: {model_name}")
            except Exception as e:
                logger.error(f"获取模型列表异常: {e}")
                model_name = "/share_data/llm_weights/Meta-Llama-3-8B"
                logger.info(f"使用默认模型: {model_name}")
    
    logger.info(f"基准测试配置:")
    logger.info(f"  - prompts 数量: {len(prompts)}")
    logger.info(f"  - 目标 QPS: {qps}")
    logger.info(f"  - 服务器地址: {server_url}")
    logger.info(f"  - 最大输出令牌数: {max_tokens}")
    logger.info(f"  - 使用模型: {model_name}")
    
    # 计算请求间隔
    interval = 1.0 / qps if qps > 0 else 0
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 记录开始时间
    start_time = time.time()
    logger.info(f"开始发送请求，测试开始时间: {datetime.fromtimestamp(start_time)}")
    
    # 创建 HTTP 会话
    timeout = aiohttp.ClientTimeout(total=300)  # 5分钟超时
    async with aiohttp.ClientSession(timeout=timeout) as session:
        tasks = []
        
        for i, prompt in tqdm(enumerate(prompts), desc="Sending requests", total=len(prompts)):
            # 创建请求任务
            task = send_request(session, server_url, prompt, max_tokens, i, start_time, model_name, logger)
            tasks.append(task)
            # # 控制请求速率
            # if interval > 0 and i < len(prompts) - 1:
            #     await asyncio.sleep(interval)
        
        # 等待所有请求完成
        logger.info("等待所有请求完成...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    successful_results = []
    failed_count = 0
    
    for result in results:
        if isinstance(result, Exception):
            logger.error(f"任务异常: {result}")
            failed_count += 1
        elif result.get('success', False):
            successful_results.append(result)
        else:
            failed_count += 1
    
    logger.info(f"测试完成统计:")
    logger.info(f"  - 成功请求: {len(successful_results)}")
    logger.info(f"  - 失败请求: {failed_count}")
    
    if not successful_results:
        logger.warning("没有成功的请求，无法生成结果文件")
        return
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = f"{output_dir}/vllm_results_{timestamp}.csv"
    
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in successful_results:
            writer.writerow({
                'request_id': result['request_id'],
                'arrived_at': result['arrived_at'],
                'num_prefill_tokens': result['num_prefill_tokens'],
                'num_decode_tokens': result['num_decode_tokens'],
            })
    
    # 保存详细结果
    json_path = f"{output_dir}/vllm_detailed_results_{timestamp}.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(successful_results, f, indent=2, ensure_ascii=False)
    
    # 计算统计信息
    total_time = max(result['arrived_at'] for result in successful_results) if successful_results else 0
    total_tokens = sum(result['num_prefill_tokens'] + result['num_decode_tokens'] for result in successful_results)
    avg_latency = sum(result['latency'] for result in successful_results) / len(successful_results)
    throughput = total_tokens / total_time if total_time > 0 else 0
    actual_qps = len(successful_results) / total_time if total_time > 0 else 0
    
    logger.info(f"性能统计:")
    logger.info(f"  - 总时间: {total_time:.2f} 秒")
    logger.info(f"  - 总令牌数: {total_tokens}")
    logger.info(f"  - 平均延迟: {avg_latency:.3f} 秒")
    logger.info(f"  - 吞吐量: {throughput:.2f} tokens/sec")
    logger.info(f"  - 实际 QPS: {actual_qps:.2f}")
    
    logger.info(f"文件输出:")
    logger.info(f"  - CSV 结果: {csv_path}")
    logger.info(f"  - 详细结果: {json_path}")
    logger.info(f"请将 CSV 文件路径更新到 config_vidur.yaml 的 trace_file 字段中")


def main():
    # 设置日志系统
    logger = setup_logging()
    
    parser = argparse.ArgumentParser(description="vLLM API 基准测试客户端")
    parser.add_argument("--prompts-file", default="validation/datasets/context_values_unique.json",
                       help="Prompts 文件路径")
    parser.add_argument("--server-url", default="http://localhost:8000/v1/completions",
                       help="vLLM 服务器 API 地址")
    parser.add_argument("--max-tokens", type=int, default=128,
                       help="最大输出令牌数")
    parser.add_argument("--qps", type=float, default=6.45,
                       help="每秒请求数")
    parser.add_argument("--output-dir", default="validation/results",
                       help="输出目录")
    parser.add_argument("--shared-config", default="validation/configs/config_shared.yaml",
                       help="共享配置文件路径")
    parser.add_argument("--vllm-config", default="validation/configs/config_vllm.yaml",
                       help="vLLM 配置文件路径")
    
    args = parser.parse_args()
    logger.info(f"启动 vLLM 客户端，参数: {vars(args)}")
    
    # 从配置文件读取参数（如果存在）
    try:
        shared_config = load_config(args.shared_config)
        vllm_config = load_config(args.vllm_config)
        merged_config = merge_configs(shared_config, vllm_config)
        
        # 使用配置文件中的参数
        qps = merged_config.get('qps', args.qps)
        max_tokens = merged_config.get('max_tokens', args.max_tokens)
        port = merged_config.get('port', 8000)
        host = merged_config.get('host', 'localhost')
        
        server_url = f"http://{host}:{port}/v1/completions"
        
        logger.info(f"使用配置文件参数: QPS={qps}, max_tokens={max_tokens}, host={host}, port={port}")
        
    except Exception as e:
        logger.warning(f"读取配置文件失败，使用命令行参数: {e}")
        qps = args.qps
        max_tokens = args.max_tokens
        server_url = args.server_url
    
    # 检查 prompts 文件
    if not Path(args.prompts_file).exists():
        logger.error(f"Prompts 文件不存在: {args.prompts_file}")
        logger.info("请先运行: python validation/data_scripts/generate_vllm_test_data.py")
        return
    
    # 运行基准测试
    try:
        asyncio.run(run_benchmark(
            prompts_file=args.prompts_file,
            server_url=server_url,
            max_tokens=max_tokens,
            qps=qps,
            output_dir=args.output_dir,
            model_name=None,  # 自动获取模型名称
            logger=logger
        ))
        logger.info("基准测试完成")
    except Exception as e:
        logger.error(f"基准测试异常: {e}", exc_info=True)


if __name__ == "__main__":
    main()
